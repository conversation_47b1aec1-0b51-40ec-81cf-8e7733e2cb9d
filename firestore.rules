rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Projects collection rules
    match /projects/{projectId} {
      // Allow public read access to all projects
      allow read: if true;
      
      // Allow create for any authenticated user or based on employee verification
      // In a real implementation, you'd verify the employee number against a secure database
      allow create: if true;
      
      // Allow update only if the user is the project leader
      // This requires the client to verify the employee number before making the request
      allow update: if resource.data.projectLeader.employeeNumber == request.auth.token.employeeNumber
                    || request.auth != null; // Allow admin updates
      
      // Allow delete only for authenticated admin users
      allow delete: if request.auth != null;
    }
    
    // Configuration collection (for departments, ranks, etc.)
    match /config/{configId} {
      // Allow read access to all
      allow read: if true;
      
      // Allow write only for authenticated admin users
      allow write: if request.auth != null;
    }
    
    // Admin users collection
    match /admins/{userId} {
      // Allow read/write only for authenticated users
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Employee verification collection (if implemented)
    match /employees/{employeeId} {
      // Allow read for verification purposes
      allow read: if true;
      
      // Allow write only for authenticated admin users
      allow write: if request.auth != null;
    }
  }
}
