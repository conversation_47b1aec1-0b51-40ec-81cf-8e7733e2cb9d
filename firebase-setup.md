# Firebase Setup Instructions

## 1. Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project"
3. Enter project name: "ebp-project-registry"
4. Enable Google Analytics (optional)
5. Create project

## 2. Enable Authentication

1. In Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to "Sign-in method" tab
4. Enable "Email/Password" provider
5. Create your first admin user:
   - Email: <EMAIL>
   - Password: password123

## 3. Setup Firestore Database

1. In Firebase Console, go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (we'll update rules later)
4. Select a location close to your users
5. Click "Done"

## 4. Configure Security Rules

1. In Firestore, go to "Rules" tab
2. Replace the default rules with the content from `firestore.rules`
3. Click "Publish"

## 5. Get Firebase Configuration

1. In Firebase Console, go to "Project settings" (gear icon)
2. Scroll down to "Your apps" section
3. Click "Web" icon to add a web app
4. Register app with name "EBP Project Registry"
5. Copy the configuration object
6. Update `src/firebase.js` with your actual config values

## 6. Deploy to Firebase Hosting (Optional)

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize: `firebase init`
   - Select "Hosting"
   - Choose your project
   - Set public directory to "dist"
   - Configure as SPA: Yes
   - Don't overwrite index.html
4. Build the app: `npm run build`
5. Deploy: `firebase deploy`

## 7. Sample Data Structure

### Project Document Example:
```json
{
  "id": "auto-generated-id",
  "title": "Implementation of Evidence-Based Pain Management Protocol",
  "department": "Emergency Medicine",
  "projectLeader": {
    "rank": "Consultant",
    "fullName": "Dr. Sarah Johnson",
    "employeeNumber": "123456"
  },
  "collaborators": [
    {
      "rank": "Senior Registrar",
      "fullName": "Dr. Michael Chen",
      "employeeNumber": "234567"
    }
  ],
  "progressStatus": "In Progress",
  "projectType": "EBP",
  "commencementDate": "2024-01-15T00:00:00.000Z",
  "completionDate": "2024-06-15T00:00:00.000Z",
  "projectDetails": "This project aims to implement evidence-based pain management protocols in the emergency department...",
  "createdAt": "2024-01-01T10:30:00.000Z"
}
```

## 8. Environment Variables

Create a `.env.local` file in your project root:

```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=your-app-id
```

Then update `src/firebase.js` to use environment variables:

```javascript
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
}
```

## 9. Testing the Application

1. Start development server: `npm run dev`
2. Navigate to the application
3. Test project registration
4. Test project listing and actions
5. Test admin login with the credentials you created
6. Test admin panel functionality

## Security Notes

- The employee number verification is implemented client-side for demo purposes
- In production, implement server-side verification using Firebase Functions
- Consider implementing role-based access control
- Use Firebase App Check for additional security
- Implement rate limiting for API calls
